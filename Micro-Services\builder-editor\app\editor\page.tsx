'use client'

import { useState } from 'react'
import { Box, Flex, useColorModeValue } from '@chakra-ui/react'
import { DragDropCanvas } from './components/Canvas/DragDropCanvas'
import { ComponentPalette } from './components/Palette/ComponentPalette'
import { PropertyPanel } from './components/Properties/PropertyPanel'
import { LayersPanel } from './components/Layers/LayersPanel'
import { EditorToolbar } from './components/Toolbar/EditorToolbar'
import { ResponsivePreview } from './components/Preview/ResponsivePreview'
import { useEditorStore } from '@/lib/stores/editorStore'

// Import new advanced features
import EnhancedElementProperties from './components/Properties/EnhancedElementProperties'
import KeyboardShortcuts from './components/KeyboardShortcuts/KeyboardShortcuts'
import HistoryPanel from './components/History/HistoryPanel'
import AutoSave from './components/AutoSave/AutoSave'

export default function EditorPage() {
  const [showPreview, setShowPreview] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const bgColor = useColorModeValue('gray.50', 'gray.900')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  const {
    currentPage,
    selectedElement,
    selectedElements,
    isPreviewMode,
    currentBreakpoint
  } = useEditorStore()

  if (showPreview) {
    return <ResponsivePreview onClose={() => setShowPreview(false)} />
  }

  return (
    <Box h="100vh" bg={bgColor} overflow="hidden">
      {/* Editor Toolbar */}
      <EditorToolbar
        onPreview={() => setShowPreview(true)}
        onToggleHistory={() => setShowHistory(!showHistory)}
      />

      <Flex h="calc(100vh - 60px)">
        {/* Left Sidebar - Component Palette */}
        <Box
          w="280px"
          borderRight="1px"
          borderColor={borderColor}
          bg="white"
          overflowY="auto"
        >
          <ComponentPalette />
        </Box>

        {/* Main Canvas Area */}
        <Flex flex="1" direction="column">
          <DragDropCanvas />
        </Flex>

        {/* Right Sidebar - Properties & Layers */}
        <Flex>
          <Box
            w="320px"
            borderLeft="1px"
            borderColor={borderColor}
            bg="white"
            overflowY="auto"
          >
            <Flex direction="column" h="100%">
              {/* Layers Panel */}
              <Box flex="1" borderBottom="1px" borderColor={borderColor}>
                <LayersPanel />
              </Box>

              {/* Enhanced Properties Panel */}
              <Box flex="1">
                {selectedElements.length > 1 ? (
                  <EnhancedElementProperties elements={selectedElements} />
                ) : selectedElement ? (
                  <EnhancedElementProperties element={selectedElement} />
                ) : (
                  <PropertyPanel />
                )}
              </Box>
            </Flex>
          </Box>

          {/* History Panel (Collapsible) */}
          {showHistory && <HistoryPanel />}
        </Flex>
      </Flex>

      {/* Global Components */}
      <KeyboardShortcuts />
      <AutoSave enabled={true} interval={30000} />
    </Box>
  )
}
