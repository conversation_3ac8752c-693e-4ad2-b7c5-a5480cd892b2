'use client'

import { useState } from 'react'
import {
  Box,
  VStack,
  Button,
  IconButton,
  Text,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Tooltip
} from '@chakra-ui/react'
import {
  SettingsIcon,
  ViewIcon,
  EditIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@chakra-ui/icons'
import { LayersPanel } from '../Layers/LayersPanel'
import { PropertyPanel } from '../Properties/PropertyPanel'
import EnhancedElementProperties from '../Properties/EnhancedElementProperties'
import { useEditorStore } from '@/lib/stores/editorStore'

interface EditorRightPanelProps {
  collapsed: boolean
  onToggleCollapse: () => void
}

export function EditorRightPanel({ collapsed, onToggleCollapse }: EditorRightPanelProps) {
  const { selectedElement, selectedElements } = useEditorStore()
  
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const textColor = useColorModeValue('gray.700', 'gray.200')

  const panelWidth = collapsed ? '60px' : '320px'

  return (
    <Box
      w={panelWidth}
      bg={bgColor}
      borderLeft="1px"
      borderColor={borderColor}
      transition="width 0.2s"
      position="relative"
      overflow="hidden"
    >
      {/* Collapse Toggle */}
      <IconButton
        aria-label={collapsed ? 'Expand panel' : 'Collapse panel'}
        icon={collapsed ? <ChevronLeftIcon /> : <ChevronRightIcon />}
        size="xs"
        position="absolute"
        top="50%"
        left="-12px"
        transform="translateY(-50%)"
        zIndex={10}
        bg={bgColor}
        border="1px"
        borderColor={borderColor}
        borderRadius="full"
        onClick={onToggleCollapse}
      />

      {!collapsed && (
        <Box h="100%">
          <Tabs variant="enclosed" size="sm" h="100%" display="flex" flexDirection="column">
            <TabList>
              <Tab>
                <ViewIcon mr={2} />
                Layers
              </Tab>
              <Tab>
                <SettingsIcon mr={2} />
                Properties
              </Tab>
            </TabList>

            <TabPanels flex="1" overflow="hidden">
              {/* Layers Tab */}
              <TabPanel p={0} h="100%" overflow="auto">
                <LayersPanel />
              </TabPanel>

              {/* Properties Tab */}
              <TabPanel p={0} h="100%" overflow="auto">
                {selectedElements.length > 1 ? (
                  <EnhancedElementProperties elements={selectedElements} />
                ) : selectedElement ? (
                  <EnhancedElementProperties element={selectedElement} />
                ) : (
                  <Box p={4}>
                    <Text fontSize="sm" color="gray.500" textAlign="center">
                      Select an element to edit its properties
                    </Text>
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      )}

      {/* Collapsed State - Icon Only */}
      {collapsed && (
        <VStack spacing={2} p={2} align="center">
          <Tooltip label="Layers" placement="left">
            <IconButton
              aria-label="Layers"
              icon={<ViewIcon />}
              size="sm"
              variant="ghost"
            />
          </Tooltip>
          <Tooltip label="Properties" placement="left">
            <IconButton
              aria-label="Properties"
              icon={<SettingsIcon />}
              size="sm"
              variant="ghost"
            />
          </Tooltip>
        </VStack>
      )}
    </Box>
  )
}
