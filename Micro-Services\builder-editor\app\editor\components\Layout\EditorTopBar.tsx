'use client'

import {
  Box,
  Flex,
  HStack,
  Button,
  IconButton,
  Text,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Divider,
  useColorModeValue,
  Badge,
  Tooltip
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  ViewIcon,
  SettingsIcon,
  ExternalLinkIcon,
  CopyIcon,
  DownloadIcon,
  RepeatIcon,
  ArrowBackIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'

export function EditorTopBar() {
  const {
    currentPage,
    isPreviewMode,
    setPreviewMode,
    currentBreakpoint,
    setCurrentBreakpoint,
    undo,
    redo,
    canUndo,
    canRedo
  } = useEditorStore()

  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const textColor = useColorModeValue('gray.700', 'gray.200')

  const handlePreview = () => {
    setPreviewMode(!isPreviewMode)
  }

  const handlePublish = () => {
    // TODO: Implement publish functionality
    console.log('Publishing site...')
  }

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving changes...')
  }

  return (
    <Box
      bg={bgColor}
      borderBottom="1px"
      borderColor={borderColor}
      px={4}
      py={2}
      zIndex={1000}
      position="relative"
      boxShadow="sm"
    >
      <Flex justify="space-between" align="center" h="56px">
        {/* Left Section - Logo and Site Info */}
        <HStack spacing={4}>
          {/* Back to Dashboard */}
          <Tooltip label="Back to Dashboard">
            <IconButton
              aria-label="Back to dashboard"
              icon={<ArrowBackIcon />}
              variant="ghost"
              size="sm"
              color={textColor}
            />
          </Tooltip>

          <Divider orientation="vertical" h="24px" />

          {/* Site Name and Page Info */}
          <Box>
            <Text fontSize="sm" fontWeight="semibold" color={textColor}>
              Demo Landing Page
            </Text>
            <Text fontSize="xs" color="gray.500">
              {currentPage?.name || 'Home Page'}
            </Text>
          </Box>

          {/* Page Status */}
          <Badge colorScheme="green" variant="subtle" size="sm">
            Draft
          </Badge>
        </HStack>

        {/* Center Section - Device Preview Controls */}
        <HStack spacing={2}>
          <Text fontSize="sm" color={textColor} mr={2}>
            Device:
          </Text>
          
          {/* Desktop */}
          <Button
            size="sm"
            variant={currentBreakpoint === 'desktop' ? 'solid' : 'outline'}
            colorScheme={currentBreakpoint === 'desktop' ? 'blue' : 'gray'}
            onClick={() => setCurrentBreakpoint('desktop')}
            leftIcon={<Box w="16px" h="12px" bg="currentColor" borderRadius="2px" />}
          >
            Desktop
          </Button>

          {/* Tablet */}
          <Button
            size="sm"
            variant={currentBreakpoint === 'tablet' ? 'solid' : 'outline'}
            colorScheme={currentBreakpoint === 'tablet' ? 'blue' : 'gray'}
            onClick={() => setCurrentBreakpoint('tablet')}
            leftIcon={<Box w="12px" h="16px" bg="currentColor" borderRadius="2px" />}
          >
            Tablet
          </Button>

          {/* Mobile */}
          <Button
            size="sm"
            variant={currentBreakpoint === 'mobile' ? 'solid' : 'outline'}
            colorScheme={currentBreakpoint === 'mobile' ? 'blue' : 'gray'}
            onClick={() => setCurrentBreakpoint('mobile')}
            leftIcon={<Box w="8px" h="16px" bg="currentColor" borderRadius="2px" />}
          >
            Mobile
          </Button>
        </HStack>

        {/* Right Section - Actions */}
        <HStack spacing={3}>
          {/* Undo/Redo */}
          <HStack spacing={1}>
            <Tooltip label="Undo">
              <IconButton
                aria-label="Undo"
                icon={<RepeatIcon transform="scaleX(-1)" />}
                size="sm"
                variant="ghost"
                isDisabled={!canUndo}
                onClick={undo}
              />
            </Tooltip>
            <Tooltip label="Redo">
              <IconButton
                aria-label="Redo"
                icon={<RepeatIcon />}
                size="sm"
                variant="ghost"
                isDisabled={!canRedo}
                onClick={redo}
              />
            </Tooltip>
          </HStack>

          <Divider orientation="vertical" h="24px" />

          {/* Preview Button */}
          <Button
            leftIcon={<ViewIcon />}
            size="sm"
            variant={isPreviewMode ? 'solid' : 'outline'}
            colorScheme={isPreviewMode ? 'blue' : 'gray'}
            onClick={handlePreview}
          >
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>

          {/* Save Button */}
          <Button
            size="sm"
            variant="outline"
            onClick={handleSave}
          >
            Save
          </Button>

          {/* Publish Button */}
          <Button
            leftIcon={<ExternalLinkIcon />}
            size="sm"
            colorScheme="green"
            onClick={handlePublish}
          >
            Publish
          </Button>

          {/* More Actions Menu */}
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="More actions"
              icon={<SettingsIcon />}
              size="sm"
              variant="ghost"
            />
            <MenuList>
              <MenuItem icon={<CopyIcon />}>
                Duplicate Page
              </MenuItem>
              <MenuItem icon={<DownloadIcon />}>
                Export
              </MenuItem>
              <MenuItem icon={<SettingsIcon />}>
                Site Settings
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>
    </Box>
  )
}
