'use client'

import { useState } from 'react'
import {
  Box,
  VStack,
  Button,
  IconButton,
  Text,
  useColorModeValue,
  Collapse,
  Tooltip
} from '@chakra-ui/react'
import {
  AddIcon,
  DragHandleIcon,
  ViewIcon,
  SettingsIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@chakra-ui/icons'
import { ComponentPalette } from '../Palette/ComponentPalette'

interface EditorSidebarProps {
  collapsed: boolean
  onToggleCollapse: () => void
}

export function EditorSidebar({ collapsed, onToggleCollapse }: EditorSidebarProps) {
  const [activeTab, setActiveTab] = useState<'sections' | 'elements' | 'templates'>('elements')
  
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const textColor = useColorModeValue('gray.700', 'gray.200')

  const sidebarWidth = collapsed ? '60px' : '280px'

  const tabs = [
    { id: 'sections', label: 'Sections', icon: DragHandleIcon },
    { id: 'elements', label: 'Elements', icon: AddIcon },
    { id: 'templates', label: 'Templates', icon: ViewIcon }
  ]

  return (
    <Box
      w={sidebarWidth}
      bg={bgColor}
      borderRight="1px"
      borderColor={borderColor}
      transition="width 0.2s"
      position="relative"
      overflow="hidden"
    >
      {/* Collapse Toggle */}
      <IconButton
        aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        icon={collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
        size="xs"
        position="absolute"
        top="50%"
        right="-12px"
        transform="translateY(-50%)"
        zIndex={10}
        bg={bgColor}
        border="1px"
        borderColor={borderColor}
        borderRadius="full"
        onClick={onToggleCollapse}
      />

      {!collapsed && (
        <VStack spacing={0} align="stretch" h="100%">
          {/* Tab Navigation */}
          <Box borderBottom="1px" borderColor={borderColor} p={2}>
            <VStack spacing={1}>
              {tabs.map((tab) => (
                <Button
                  key={tab.id}
                  leftIcon={<tab.icon />}
                  size="sm"
                  variant={activeTab === tab.id ? 'solid' : 'ghost'}
                  colorScheme={activeTab === tab.id ? 'blue' : 'gray'}
                  w="100%"
                  justifyContent="flex-start"
                  onClick={() => setActiveTab(tab.id as any)}
                >
                  {tab.label}
                </Button>
              ))}
            </VStack>
          </Box>

          {/* Tab Content */}
          <Box flex="1" overflow="hidden">
            {activeTab === 'sections' && (
              <Box p={3}>
                <Text fontSize="sm" fontWeight="semibold" color={textColor} mb={3}>
                  Add Sections
                </Text>
                <VStack spacing={2} align="stretch">
                  <Button size="sm" variant="outline" leftIcon={<AddIcon />}>
                    Hero Section
                  </Button>
                  <Button size="sm" variant="outline" leftIcon={<AddIcon />}>
                    Features Section
                  </Button>
                  <Button size="sm" variant="outline" leftIcon={<AddIcon />}>
                    Testimonials
                  </Button>
                  <Button size="sm" variant="outline" leftIcon={<AddIcon />}>
                    Contact Section
                  </Button>
                  <Button size="sm" variant="outline" leftIcon={<AddIcon />}>
                    Gallery Section
                  </Button>
                </VStack>
              </Box>
            )}

            {activeTab === 'elements' && (
              <Box h="100%" overflow="auto">
                <ComponentPalette />
              </Box>
            )}

            {activeTab === 'templates' && (
              <Box p={3}>
                <Text fontSize="sm" fontWeight="semibold" color={textColor} mb={3}>
                  Templates
                </Text>
                <VStack spacing={2} align="stretch">
                  <Button size="sm" variant="outline">
                    Business Template
                  </Button>
                  <Button size="sm" variant="outline">
                    Portfolio Template
                  </Button>
                  <Button size="sm" variant="outline">
                    Blog Template
                  </Button>
                  <Button size="sm" variant="outline">
                    E-commerce Template
                  </Button>
                </VStack>
              </Box>
            )}
          </Box>
        </VStack>
      )}

      {/* Collapsed State - Icon Only */}
      {collapsed && (
        <VStack spacing={2} p={2} align="center">
          {tabs.map((tab) => (
            <Tooltip key={tab.id} label={tab.label} placement="right">
              <IconButton
                aria-label={tab.label}
                icon={<tab.icon />}
                size="sm"
                variant={activeTab === tab.id ? 'solid' : 'ghost'}
                colorScheme={activeTab === tab.id ? 'blue' : 'gray'}
                onClick={() => setActiveTab(tab.id as any)}
              />
            </Tooltip>
          ))}
        </VStack>
      )}
    </Box>
  )
}
